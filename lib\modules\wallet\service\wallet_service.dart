import 'package:get/get.dart';
import 'package:rolio/common/utils/logger.dart';
import 'package:rolio/common/utils/error_handler.dart';
import 'package:rolio/common/constants/error_codes.dart';
import 'package:rolio/modules/wallet/model/wallet.dart';
import 'package:rolio/modules/wallet/model/transaction_record.dart';
import 'package:rolio/modules/wallet/repository/wallet_repository.dart';
import 'package:rolio/manager/global_state.dart';

/// 钱包服务
class WalletService extends GetxService {
  /// 钱包Repository
  final IWalletRepository _repository;
  
  /// 全局状态
  final GlobalState _globalState = Get.find<GlobalState>();
  
  /// 当前钱包状态
  final Rx<Wallet?> currentWallet = Rx<Wallet?>(null);
  
  /// 交易记录列表
  final RxList<TransactionRecord> transactions = <TransactionRecord>[].obs;
  
  /// 是否正在加载
  final RxBool isLoading = false.obs;
  
  /// 是否正在处理交易
  final RxBool isProcessingTransaction = false.obs;

  /// 构造函数
  WalletService(this._repository);

  @override
  void onInit() {
    super.onInit();
    LogUtil.debug('WalletService: 初始化');
    
    // 监听用户状态变化
    _globalState.currentUser.listen((user) {
      if (user != null) {
        // 用户登录时初始化钱包
        _initializeUserWallet();
      } else {
        // 用户登出时清空钱包状态
        _clearWalletState();
      }
    });
  }

  /// 初始化用户钱包
  Future<void> _initializeUserWallet() async {
    try {
      LogUtil.debug('WalletService: 初始化用户钱包');
      
      // 首先尝试获取现有钱包
      await refreshWallet();
      
      // 如果钱包不存在，则初始化新钱包
      if (currentWallet.value == null) {
        LogUtil.debug('WalletService: 钱包不存在，创建新钱包');
        await _repository.initializeWallet();
        await refreshWallet();
      }
    } catch (e) {
      LogUtil.error('WalletService: 初始化用户钱包失败: $e');
      ErrorHandler.handleException(
        AppException('钱包初始化失败', originalError: e, code: ErrorCodes.BUSINESS_ERROR),
      );
    }
  }

  /// 清空钱包状态
  void _clearWalletState() {
    LogUtil.debug('WalletService: 清空钱包状态');
    currentWallet.value = null;
    transactions.clear();
  }

  /// 刷新钱包信息
  Future<bool> refreshWallet() async {
    try {
      isLoading.value = true;
      LogUtil.debug('WalletService: 刷新钱包信息');
      
      final wallet = await _repository.getWallet();
      currentWallet.value = wallet;
      
      if (wallet != null) {
        LogUtil.debug('WalletService: 钱包信息刷新成功，余额: ${wallet.diamonds}');
        
        // 同时刷新交易记录
        await refreshTransactionHistory();
        return true;
      } else {
        LogUtil.warn('WalletService: 钱包信息不存在');
        return false;
      }
    } catch (e) {
      LogUtil.error('WalletService: 刷新钱包信息失败: $e');
      ErrorHandler.handleException(
        AppException('获取钱包信息失败', originalError: e, code: ErrorCodes.BUSINESS_ERROR),
      );
      return false;
    } finally {
      isLoading.value = false;
    }
  }

  /// 刷新交易记录
  Future<bool> refreshTransactionHistory({int? limit}) async {
    try {
      LogUtil.debug('WalletService: 刷新交易记录');
      
      final records = await _repository.getTransactionHistory(limit: limit);
      transactions.assignAll(records);
      
      LogUtil.debug('WalletService: 交易记录刷新成功，数量: ${records.length}');
      return true;
    } catch (e) {
      LogUtil.error('WalletService: 刷新交易记录失败: $e');
      ErrorHandler.handleException(
        AppException('获取交易记录失败', originalError: e, code: ErrorCodes.BUSINESS_ERROR),
      );
      return false;
    }
  }

  /// 消费钻石
  Future<bool> spendDiamonds(int amount, String description) async {
    try {
      if (amount <= 0) {
        LogUtil.warn('WalletService: 消费金额必须大于0: $amount');
        ErrorHandler.handleException(
          AppException('消费金额无效', code: ErrorCodes.BUSINESS_ERROR),
        );
        return false;
      }

      isProcessingTransaction.value = true;
      LogUtil.debug('WalletService: 消费钻石，金额: $amount，描述: $description');

      final wallet = currentWallet.value;
      if (wallet == null) {
        LogUtil.error('WalletService: 钱包信息不存在');
        ErrorHandler.handleException(
          AppException('钱包信息不存在', code: ErrorCodes.BUSINESS_ERROR),
        );
        return false;
      }

      if (!wallet.hasEnoughDiamonds(amount)) {
        LogUtil.warn('WalletService: 余额不足，需要: $amount，当前: ${wallet.diamonds}');
        ErrorHandler.handleException(
          AppException('余额不足', code: ErrorCodes.BUSINESS_ERROR),
        );
        return false;
      }

      // 计算新余额
      final newBalance = wallet.diamonds - amount;
      
      // 更新余额
      final updateSuccess = await _repository.updateBalance(newBalance);
      if (!updateSuccess) {
        LogUtil.error('WalletService: 更新余额失败');
        return false;
      }

      // 创建交易记录
      final transaction = TransactionRecord(
        id: LocalWalletRepository.generateTransactionId(),
        amount: -amount, // 负数表示支出
        type: TransactionType.spend,
        description: description,
        timestamp: DateTime.now(),
        balanceAfter: newBalance,
      );

      // 添加交易记录
      final transactionSuccess = await _repository.addTransaction(transaction);
      if (!transactionSuccess) {
        LogUtil.error('WalletService: 添加交易记录失败');
        // 交易记录失败不影响主要功能，继续执行
      }

      // 更新本地状态
      currentWallet.value = wallet.spendDiamonds(amount);
      
      // 刷新交易记录
      await refreshTransactionHistory(limit: 50);

      LogUtil.info('WalletService: 消费钻石成功，金额: $amount，余额: $newBalance');
      return true;
    } catch (e) {
      LogUtil.error('WalletService: 消费钻石失败: $e');
      ErrorHandler.handleException(
        AppException('消费失败', originalError: e, code: ErrorCodes.BUSINESS_ERROR),
      );
      return false;
    } finally {
      isProcessingTransaction.value = false;
    }
  }

  /// 增加钻石
  Future<bool> addDiamonds(int amount, String description, {TransactionType type = TransactionType.earn}) async {
    try {
      if (amount <= 0) {
        LogUtil.warn('WalletService: 增加金额必须大于0: $amount');
        ErrorHandler.handleException(
          AppException('增加金额无效', code: ErrorCodes.BUSINESS_ERROR),
        );
        return false;
      }

      isProcessingTransaction.value = true;
      LogUtil.debug('WalletService: 增加钻石，金额: $amount，描述: $description');

      final wallet = currentWallet.value;
      if (wallet == null) {
        LogUtil.error('WalletService: 钱包信息不存在');
        ErrorHandler.handleException(
          AppException('钱包信息不存在', code: ErrorCodes.BUSINESS_ERROR),
        );
        return false;
      }

      // 计算新余额
      final newBalance = wallet.diamonds + amount;
      
      // 更新余额
      final updateSuccess = await _repository.updateBalance(newBalance);
      if (!updateSuccess) {
        LogUtil.error('WalletService: 更新余额失败');
        return false;
      }

      // 创建交易记录
      final transaction = TransactionRecord(
        id: LocalWalletRepository.generateTransactionId(),
        amount: amount, // 正数表示收入
        type: type,
        description: description,
        timestamp: DateTime.now(),
        balanceAfter: newBalance,
      );

      // 添加交易记录
      final transactionSuccess = await _repository.addTransaction(transaction);
      if (!transactionSuccess) {
        LogUtil.error('WalletService: 添加交易记录失败');
        // 交易记录失败不影响主要功能，继续执行
      }

      // 更新本地状态
      currentWallet.value = wallet.addDiamonds(amount);
      
      // 刷新交易记录
      await refreshTransactionHistory(limit: 50);

      LogUtil.info('WalletService: 增加钻石成功，金额: $amount，余额: $newBalance');
      return true;
    } catch (e) {
      LogUtil.error('WalletService: 增加钻石失败: $e');
      ErrorHandler.handleException(
        AppException('增加钻石失败', originalError: e, code: ErrorCodes.BUSINESS_ERROR),
      );
      return false;
    } finally {
      isProcessingTransaction.value = false;
    }
  }

  /// 检查是否有足够的钻石
  bool hasEnoughDiamonds(int amount) {
    final wallet = currentWallet.value;
    return wallet?.hasEnoughDiamonds(amount) ?? false;
  }

  /// 获取当前余额
  int get currentBalance => currentWallet.value?.diamonds ?? 0;

  /// 清空交易记录
  Future<bool> clearTransactionHistory() async {
    try {
      LogUtil.debug('WalletService: 清空交易记录');
      
      final success = await _repository.clearTransactionHistory();
      if (success) {
        transactions.clear();
        LogUtil.info('WalletService: 交易记录清空成功');
      } else {
        LogUtil.error('WalletService: 交易记录清空失败');
      }
      
      return success;
    } catch (e) {
      LogUtil.error('WalletService: 清空交易记录异常: $e');
      ErrorHandler.handleException(
        AppException('清空交易记录失败', originalError: e, code: ErrorCodes.BUSINESS_ERROR),
      );
      return false;
    }
  }
}
