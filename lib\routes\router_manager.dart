import 'package:get/get.dart';
import 'package:rolio/common/interfaces/chat_service_interface.dart';
import 'package:rolio/common/services/interface_providers.dart';
import 'package:rolio/common/utils/logger.dart';
import 'package:rolio/common/utils/toast_util.dart';
import 'package:rolio/modules/chat/binding/chat_binding.dart';
import 'package:rolio/modules/chat/service/chat_service.dart';
import 'package:rolio/modules/chat/view/chat_page.dart';
import 'package:rolio/modules/home/<USER>/home_binding.dart';
import 'package:rolio/modules/home/<USER>/home_page.dart';
import 'package:rolio/modules/login/binding/login_binding.dart';
import 'package:rolio/modules/login/view/login_page.dart';
import 'package:rolio/modules/role/binding/recommend_binding.dart';
import 'package:rolio/modules/role/binding/role_binding.dart';
import 'package:rolio/modules/role/binding/search_binding.dart';
import 'package:rolio/modules/role/view/favorite_page.dart';
import 'package:rolio/modules/role/view/recommend_page.dart';
import 'package:rolio/modules/role/view/role_page.dart';
import 'package:rolio/modules/role/view/search_page.dart';
import 'package:rolio/modules/sessions/binding/sessions_binding.dart';
import 'package:rolio/modules/sessions/view/sessions_page.dart';
import 'package:rolio/modules/splash/binding/splash_binding.dart';
import 'package:rolio/modules/splash/view/splash_page.dart';
import 'package:rolio/modules/user/binding/user_binding.dart';
import 'package:rolio/modules/user/view/user_page.dart';
import 'package:rolio/modules/wallet/binding/wallet_binding.dart';
import 'package:rolio/modules/wallet/view/wallet_page.dart';
import 'package:rolio/routes/routes.dart';


/// 路由中间件接口
abstract class RouteMiddleware {
  /// 路由前置处理
  /// 
  /// 返回true表示通过，false表示拦截
  Future<bool> beforeEnter(String routeName, {dynamic arguments});
  
  /// 路由后置处理
  Future<void> afterEnter(String routeName, {dynamic arguments});
  
  /// 路由离开处理
  /// 
  /// 返回true表示允许离开，false表示拦截
  Future<bool> beforeLeave(String fromRoute, String toRoute);
}

/// 权限中间件
class AuthMiddleware implements RouteMiddleware {
  /// 获取用户权限列表
  /// 
  /// 实际项目中应该从用户服务或存储中获取
  List<String> get _userPermissions {
    // 模拟用户权限，实际项目中应该从用户服务获取
    return ['auth']; // 默认有基础权限
  }
  
  @override
  Future<bool> beforeEnter(String routeName, {dynamic arguments}) async {
    LogUtil.debug('AuthMiddleware: 检查路由[$routeName]权限');
    
    // 检查权限
    final hasPermission = Routes.checkPermissions(routeName, _userPermissions);
    if (!hasPermission) {
      LogUtil.warn('用户缺少访问路由[$routeName]的权限');
      // 可以在这里添加提示或重定向到登录页面
      ToastUtil.error('error, you do not have permission to access this page');
      return false;
    }
    
    return true;
  }
  
  @override
  Future<void> afterEnter(String routeName, {dynamic arguments}) async {
    // 路由进入后的处理
    LogUtil.debug('AuthMiddleware: 路由[$routeName]已进入');
  }
  
  @override
  Future<bool> beforeLeave(String fromRoute, String toRoute) async {
    // 可以在这里添加离开前的确认逻辑
    return true;
  }
}

/// 参数验证中间件
class ParamsMiddleware implements RouteMiddleware {
  @override
  Future<bool> beforeEnter(String routeName, {dynamic arguments}) async {
    LogUtil.debug('ParamsMiddleware: 验证路由[$routeName]参数');
    
    // 验证参数
    final isValid = Routes.validateParams(routeName, arguments);
    if (!isValid) {
      LogUtil.error('路由[$routeName]参数验证失败: $arguments');
      ToastUtil.error('error, page parameters are incomplete, please check');
      return false;
    }
    
    return true;
  }
  
  @override
  Future<void> afterEnter(String routeName, {dynamic arguments}) async {
    // 路由进入后的处理
  }
  
  @override
  Future<bool> beforeLeave(String fromRoute, String toRoute) async {
    return true;
  }
}

/// 路由管理器
///
/// 管理应用的所有路由配置
class RouterManager {
  /// 初始路由
  static const String initialRoute = Routes.splashScreen; // 修改初始路由为启动页
  
  /// 路由中间件列表
  static final List<RouteMiddleware> _middlewares = [
    AuthMiddleware(),
    ParamsMiddleware(),
  ];
  
  /// 获取路由页面配置
  static List<GetPage> get pages => [
    GetPage(
      name: Routes.splashScreen,
      page: () => const SplashPage(),
      binding: SplashBinding(),
      transition: Transition.fadeIn,
    ),
    GetPage(
      name: Routes.homeScreen,
      page: () => const HomePage(),
      bindings: [
        // 先注册接口的默认实现，确保基础依赖可用
        InterfaceProvidersBinding(),
        // 只注册Home相关的控制器
        HomeBinding(),
      ],
      transition: Transition.fadeIn,
    ),
    GetPage(
      name: Routes.chatScreen,
      page: () => const ChatPage(),
      binding: ChatBinding(), // 会替换默认的接口实现为完整实现
      transition: Transition.noTransition, // 无动画效果，硬跳转
    ),
    GetPage(
      name: Routes.recommendScreen,
      page: () => const RecommendPage(),
      binding: RecommendBinding(),
      transition: Transition.fadeIn,
    ),
    GetPage(
      name: Routes.roleDetailScreen,
      page: () => const RolePage(),
      binding: RoleBinding(),
      transition: Transition.fadeIn,
    ),
    GetPage(
      name: Routes.sessionsScreen,
      page: () => const SessionsPage(),
      binding: SessionsBinding(),
      transition: Transition.fadeIn,
    ),
    GetPage(
      name: Routes.loginScreen,
      page: () => const LoginPage(),
      binding: LoginBinding(),
      transition: Transition.fadeIn,
    ),
    GetPage(
      name: Routes.userScreen,
      page: () => const UserPage(),
      binding: UserBinding(),
      transition: Transition.fadeIn,
    ),
    GetPage(
      name: Routes.favoriteScreen,
      page: () => const FavoritePage(),
      binding: RoleBinding(),
      transition: Transition.fadeIn,
    ),
    GetPage(
      name: Routes.searchScreen,
      page: () => SearchPage(),
      binding: SearchBinding(),
      transition: Transition.fadeIn,
    ),
    GetPage(
      name: Routes.walletScreen,
      page: () => const WalletPage(),
      binding: WalletBinding(),
      transition: Transition.fadeIn,
    ),
  ];
  
  /// 执行路由中间件
  static Future<bool> _runMiddlewares(String routeName, {dynamic arguments}) async {
    for (final middleware in _middlewares) {
      final result = await middleware.beforeEnter(routeName, arguments: arguments);
      if (!result) {
        return false;
      }
    }
    return true;
  }
  
  /// 执行路由离开中间件
  static Future<bool> _runLeaveMiddlewares(String fromRoute, String toRoute) async {
    for (final middleware in _middlewares) {
      final result = await middleware.beforeLeave(fromRoute, toRoute);
      if (!result) {
        return false;
      }
    }
    return true;
  }
  
  /// 执行路由后置中间件
  static Future<void> _runAfterMiddlewares(String routeName, {dynamic arguments}) async {
    for (final middleware in _middlewares) {
      await middleware.afterEnter(routeName, arguments: arguments);
    }
  }
  
  /// 导航到指定路由
  static Future<T?>? navigateTo<T>(String routeName, {dynamic arguments}) async {
    try {
      LogUtil.info('准备导航到路由: $routeName, 参数: $arguments');
      
      // 运行离开中间件
      final currentRoute = Get.currentRoute;
      final canLeave = await _runLeaveMiddlewares(currentRoute, routeName);
      if (!canLeave) {
        LogUtil.warn('中间件阻止离开当前路由: $currentRoute');
        return null;
      }
      
      // 运行中间件
      final canProceed = await _runMiddlewares(routeName, arguments: arguments);
      if (!canProceed) {
        LogUtil.warn('中间件阻止导航到路由: $routeName');
        return null;
      }
      
      // 特殊处理聊天页面的导航
      if (routeName == Routes.chatScreen) {
        LogUtil.info('准备导航到聊天页面，确保聊天服务正确加载');
        
        // 在导航前预先清除可能存在的旧实例，确保ChatBinding能够正确注册新实例
        _cleanupChatServices();
        
        // 预先执行ChatBinding，确保聊天服务正确初始化
        // 但不重复初始化WebSocketService
        final binding = ChatBinding();
        binding.dependencies();
        
        LogUtil.info('聊天服务已预先初始化，现在导航到聊天页面');
      }
      
      final result = await Get.toNamed<T>(routeName, arguments: arguments);
      
      // 运行后置中间件
      await _runAfterMiddlewares(routeName, arguments: arguments);
      
      return result;
    } catch (e) {
      LogUtil.error('导航错误: $e');
      return null;
    }
  }
  
  /// 清理聊天相关服务，确保ChatBinding能够正确注册新实例
  static void _cleanupChatServices() {
    try {
      // 清除IChatService实例
      if (Get.isRegistered<IChatService>()) {
        LogUtil.debug('清除已存在的IChatService实例');
        Get.delete<IChatService>(force: true);
      }
      
      // 清除ChatService实例
      if (Get.isRegistered<ChatService>()) {
        LogUtil.debug('清除已存在的ChatService实例');
        Get.delete<ChatService>(force: true);
      }
    } catch (e) {
      LogUtil.error('清理聊天服务失败: $e');
    }
  }
  
  /// 替换当前路由
  static Future<T?>? replaceTo<T>(String routeName, {dynamic arguments}) async {
    try {
      // 运行中间件
      final canProceed = await _runMiddlewares(routeName, arguments: arguments);
      if (!canProceed) {
        LogUtil.warn('中间件阻止替换到路由: $routeName');
        return null;
      }
      
      // 特殊处理聊天页面的导航
      if (routeName == Routes.chatScreen) {
        _cleanupChatServices();
        
        // 预先执行ChatBinding
        final binding = ChatBinding();
        binding.dependencies();
      }
      
      final result = await Get.offNamed<T>(routeName, arguments: arguments);
      
      // 运行后置中间件
      await _runAfterMiddlewares(routeName, arguments: arguments);
      
      return result;
    } catch (e) {
      LogUtil.error('路由替换错误: $e');
      return null;
    }
  }
  
  /// 清除所有路由并导航到指定路由
  static Future<T?>? clearAndNavigateTo<T>(String routeName, {dynamic arguments}) async {
    try {
      // 运行中间件
      final canProceed = await _runMiddlewares(routeName, arguments: arguments);
      if (!canProceed) {
        LogUtil.warn('中间件阻止清除并导航到路由: $routeName');
        return null;
      }
      
      // 特殊处理聊天页面的导航
      if (routeName == Routes.chatScreen) {
        _cleanupChatServices();
        
        // 预先执行ChatBinding
        final binding = ChatBinding();
        binding.dependencies();
      }
      
      final result = await Get.offAllNamed<T>(routeName, arguments: arguments);
      
      // 运行后置中间件
      await _runAfterMiddlewares(routeName, arguments: arguments);
      
      return result;
    } catch (e) {
      LogUtil.error('清除路由错误: $e');
      return null;
    }
  }
  
  /// 返回上一页
  static void goBack<T>({T? result}) {
    if (Get.currentRoute != initialRoute && Get.currentRoute.isNotEmpty) {
      Get.back<T>(result: result);
    }
  }
}